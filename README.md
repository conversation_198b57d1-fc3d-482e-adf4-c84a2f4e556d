# Aria2 Docker 下载器

基于 Docker 的 [Aria2](https://github.com/aria2/aria2) 下载器，集成 [YAAW](https://github.com/binux/yaaw) Web 界面和 [Caddy](https://caddyserver.com/) 反向代理。

## 快速开始

最简单的运行方式：

```bash
docker run -d --name aria2-docker -p 8080:8080 -p 8090:8090 aria2-docker
```

完整配置运行：

```bash
docker run -d \
    --name aria2-docker \
    -p 8080:8080 \                         # YAAW Web 界面端口
    -p 8090:8090 \                         # 自定义 Web 前端端口（包含 API 代理）
    -v /path/to/downloads:/aria2/data \    # 下载文件存储目录
    -v /path/to/config:/aria2/conf \       # Aria2 配置文件目录
    -e PUID=1000 \                         # 运行用户的 UID，用于文件权限管理
    -e PGID=1000 \                         # 运行用户的 GID，用于文件权限管理
    aria2-docker
```

## 使用说明

1. 启动容器后，可以访问以下界面：
   - `http://localhost:8080` - YAAW Web 界面（经典下载管理界面）
   - `http://localhost:8090` - 自定义 Web 前端（操作记录管理界面）
2. 在界面中可以添加下载任务，支持：
   - HTTP/HTTPS 链接
   - FTP 链接
   - BitTorrent 种子文件
   - Magnet 磁力链接
3. 下载的文件将保存在 `/aria2/data` 目录中
4. 可以通过修改 `/aria2/conf/aria2.conf` 文件来自定义 Aria2 配置

## 构建与运行

本项目包含两个独立的可执行文件：
- `aria2-api`：RESTful HTTP API 服务，入口为 `api.go`
- `aria2-hook`：命令行文件移动与日志工具，入口为 `hook.go`

### 构建

```bash
# 构建 API 服务
cd /home/<USER>/code/aria2-docker
GO111MODULE=on go build -o aria2-api api.go db.go

# 构建 CLI 工具
GO111MODULE=on go build -o aria2-hook hook.go db.go
```

### 运行

```bash
# 启动 API 服务
./aria2-api

# 使用 CLI 工具
./aria2-hook <gid> <file_count> <first_file_path>
```

## 前端构建与开发

### 本地开发
```bash
cd web
npm install
npm run dev
```
访问 http://localhost:5173 进行开发调试。

### 生产构建
```bash
cd web
npm run build
```
构建产物在 `web/dist`，可直接集成到 Docker 镜像。

### Docker 一体化构建
```bash
docker build -t aria2-docker .
```
镜像会自动完成前端和后端的编译，前端静态资源位于 `/app/web`。

### 相关依赖
- Node.js 20+、npm 9+
- Vite 5、Vue 3
- 详见 web/package.json

## 注意事项
- 两个入口文件都依赖 `db.go`，请勿删除。
- 若遇到依赖问题，请先执行 `go mod tidy`。
- 需要 Go 1.18+ 环境。

## 相关项目

- [Aria2](https://github.com/aria2/aria2) - 多协议下载工具
- [YAAW](https://github.com/binux/yaaw) - Aria2 Web 界面
- [Caddy](https://caddyserver.com/) - 现代化 Web 服务器
