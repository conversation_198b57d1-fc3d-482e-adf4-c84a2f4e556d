package main

import (
	"database/sql"
	"fmt"
	"log"
	"os/exec"

	_ "github.com/mattn/go-sqlite3"
)

// MoveFileExec 统一的文件移动实现，供 CLI 和 API 共用
func MoveFileExec(source_file string, target_file string) error {
	log.Println("run command: mv " + source_file + " " + target_file)
	cmd := exec.Command("mv", source_file, target_file)
	return cmd.Run()
}

type OperationLog struct {
	ID           int64  `json:"id"`
	Gid          string `json:"gid"`
	FileCountNum int    `json:"file_count_num"`
	SourceFile   string `json:"source_file"`
	TargetFile   string `json:"target_file"`
	Status       string `json:"status"`
	ErrorMessage string `json:"error_message"`
	CreatedAt    string `json:"created_at"`
	FinishedAt   string `json:"finished_at"`
	OpType       string `json:"op_type"`
}

var db *sql.DB

// InitDB 初始化数据库并创建表
func InitDB(dbPath string) (*sql.DB, error) {
	database, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return nil, err
	}
	// 检查表是否已存在
	var tableName string
	err = database.QueryRow("SELECT name FROM sqlite_master WHERE type='table' AND name='file_operations'").Scan(&tableName)
	if err == sql.ErrNoRows {
		log.Println("[DB] file_operations 表不存在，正在创建...")
		createTableSQL := `CREATE TABLE file_operations (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			gid TEXT,
			file_count_num INTEGER,
			source_file TEXT,
			target_file TEXT,
			timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
			status TEXT,
			error_message TEXT
		);`
		_, err = database.Exec(createTableSQL)
		if err != nil {
			log.Printf("[DB] 创建 file_operations 表失败: %v", err)
			return nil, err
		}
		log.Println("[DB] file_operations 表创建成功")
	} else if err != nil {
		log.Printf("[DB] 检查 file_operations 表时出错: %v", err)
		return nil, err
	} else {
		log.Println("[DB] file_operations 表已存在，跳过创建")
	}
	return database, nil
}

// InsertFileOperation 插入操作记录
func InsertFileOperation(db *sql.DB, gid string, fileCount int, sourceFile, targetFile, status, errorMsg string) error {
	if db == nil {
		return fmt.Errorf("db is not initialized")
	}
	stmt := `INSERT INTO file_operations (gid, file_count_num, source_file, target_file, status, error_message) VALUES (?, ?, ?, ?, ?, ?)`
	_, err := db.Exec(stmt, gid, fileCount, sourceFile, targetFile, status, errorMsg)
	return err
}
