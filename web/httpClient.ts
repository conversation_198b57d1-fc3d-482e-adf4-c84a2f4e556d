import { OperationLog, OperationLogQuery, OperationLogResponse } from './types';

// 实际后端 API 地址（Vite 代理到 /api）
const API_BASE = '/api/operations';

export async function fetchOperationLogs(query: OperationLogQuery): Promise<OperationLogResponse> {
  const params = new URLSearchParams();
  params.set('page', String(query.page));
  params.set('limit', String(query.pageSize));
  // 只传递分页参数
  const resp = await fetch(`${API_BASE}?${params.toString()}`);
  if (!resp.ok) throw new Error('网络错误: ' + resp.status);
  const data = await resp.json();
  return {
    total: data.total,
    records: data.records,
  };
}

export async function exportLogsToCSV(logs: OperationLog[]): Promise<string> {
  const header = 'ID,GID,文件数,源文件,目标文件,状态,错误信息,时间\n';
  return (
    header +
    logs
      .map(
        (log) =>
          `${log.id},${log.gid},${log.file_count_num},${log.source_file},${log.target_file},${log.status},${log.error_message},${log.timestamp}`
      )
      .join('\n')
  );
}
