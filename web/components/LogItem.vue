<template>
  <tr :class="rowClass">
    <td class="whitespace-nowrap text-xs md:text-sm">{{ log.timestamp.replace('T', ' ').slice(0, 19) }}</td>
    <td class="truncate max-w-xs" :title="log.source_file">{{ log.source_file }}</td>
    <td class="truncate max-w-xs" :title="log.target_file">{{ log.target_file }}</td>
    <td>
      <span :class="statusClass">{{ log.status === 'success' ? '成功' : '失败' }}</span>
    </td>
    <td>
      <span v-if="log.error_message" class="text-xs text-red-500">{{ log.error_message }}</span>
      <span v-else class="text-gray-400 text-xs">-</span>
    </td>
  </tr>
</template>

<script setup lang="ts">
import type { OperationLog } from '../types';
import { computed } from 'vue';
const props = defineProps<{ log: OperationLog }>();
const statusClass = computed(() =>
  props.log.status === 'success'
    ? 'text-green-600 font-bold'
    : 'text-red-600 font-bold'
);
const rowClass = computed(() =>
  props.log.status === 'success'
    ? 'bg-green-50'
    : 'bg-red-50'
);
</script>
