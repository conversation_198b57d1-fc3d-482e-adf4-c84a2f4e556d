<template>
  <div class="flex flex-col md:flex-row md:items-end gap-2 md:gap-4 mb-4">
    <div>
      <label class="block text-sm font-medium mb-1">日期范围</label>
      <input type="date" v-model="start" class="input input-bordered input-sm" />
      <span class="mx-1">-</span>
      <input type="date" v-model="end" class="input input-bordered input-sm" />
    </div>
    <div>
      <label class="block text-sm font-medium mb-1">状态</label>
      <select v-model="status" class="input input-bordered input-sm">
        <option value="all">全部</option>
        <option value="success">成功</option>
        <option value="failed">失败</option>
      </select>
    </div>
    <div class="flex-1">
      <label class="block text-sm font-medium mb-1">文件名搜索</label>
      <input v-model="keyword" type="text" placeholder="输入文件名..." class="input input-bordered input-sm w-full" />
    </div>
    <button @click="onSearch" class="btn btn-primary btn-sm mt-2 md:mt-0">筛选</button>
    <button @click="onReset" class="btn btn-outline btn-sm mt-2 md:mt-0">重置</button>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, defineEmits, defineProps } from 'vue';
const emits = defineEmits(['search']);
const props = defineProps<{
  status: string;
  keyword: string;
  dateRange: [string, string];
}>();
const start = ref(props.dateRange[0]);
const end = ref(props.dateRange[1]);
const status = ref(props.status);
const keyword = ref(props.keyword);

watch(() => props.dateRange, (v) => {
  start.value = v[0];
  end.value = v[1];
});

const onSearch = () => {
  emits('search', {
    status: status.value,
    keyword: keyword.value,
    dateRange: [start.value, end.value],
  });
};
const onReset = () => {
  start.value = '';
  end.value = '';
  status.value = 'all';
  keyword.value = '';
  onSearch();
};
</script>

<style scoped>
.input {
  @apply border rounded px-2 py-1;
}
.btn {
  @apply px-3 py-1 rounded;
}
</style>
