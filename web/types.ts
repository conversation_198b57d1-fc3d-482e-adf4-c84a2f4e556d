export interface OperationLog {
  id: number;
  gid: string;
  file_count_num: number;
  source_file: string;
  target_file: string;
  timestamp: string;
  status: 'success' | 'failed';
  error_message: string;
}

export interface OperationLogQuery {
  page: number;
  pageSize: number;
  status?: 'all' | 'success' | 'failed';
  keyword?: string;
  dateRange?: [string, string]; // ISO 日期字符串
}

export interface OperationLogResponse {
  total: number;
  records: OperationLog[];
}
