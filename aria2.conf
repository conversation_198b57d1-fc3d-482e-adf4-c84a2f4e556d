enable-rpc=true
rpc-allow-origin-all=true
# allow external access, if false, only listen to local port
rpc-listen-all=true
# rpc port, modified only when the default port is occupied
rpc-listen-port=6845
disable-ipv6=true
# maximum simultaneous downloads (number of tasks), route suggested value: 3
max-concurrent-downloads=5
continue=true
max-connection-per-server=5
# minimum file fragment size, the maximum number of download threads depends on how many slices can be divided, which is important for small files.
min-split-size=10M
# maximum number of threads, route suggested value: 5
split=10
# download speed limit
max-overall-download-limit=0
max-download-limit=0
# upload speed limit
max-overall-upload-limit=0
max-upload-limit=0
# file save path, default is the current root location
dir=/aria2/data
# file pre-allocation, can effectively reduce file fragmentation and improve disk performance. the disadvantage is that the pre-allocation time is longer.
# none < falloc ? trunc « prealloc, falloc and trunc require file system and kernel support
file-allocation=prealloc

# general options
# log=/aria2/conf/aria2.log
# you can set either debug, info, notice, warn or error.
# log-level=warn
console-log-level=error

## progress save related ##
# read the download task from the session file
input-file=/aria2/conf/aria2.session
# save the 'error/unfinished' download task to the session file when exiting aria2
save-session=/aria2/conf/aria2.session
# timely save session, 0 is saved when exiting, need to be 1.16.1 or later, default: 0
save-session-interval=10

# bt trackers from https://raw.githubusercontent.com/ngosang/trackerslist/master/trackers_best_ip.txt
# Project url: https://github.com/ngosang/trackerslist
bt-tracker=udp://************:6969/announce,udp://**************:6969/announce,udp://**************:2710/announce,udp://**************:2710/announce,udp://*************:1337/announce,udp://**************:80/announce,udp://**************:1337/announce,udp://************:6969/announce,udp://************:6969/announce,udp://*************:2710/announce,udp://************:1337/announce,udp://*************:80/announce,udp://*************:6969/announce,udp://*************:2710/announce,udp://**************:451/announce,udp://***************:6969/announce,udp://***********:6969/announce,udp://*************:6969/announce,udp://*************:6961/announce,udp://************:80/announce