#!/bin/sh

conf_path=/aria2/conf
conf_copy_path=/aria2/conf-copy
data_path=/aria2/data

# If config does not exist - use default
if [ ! -f $conf_path/aria2.conf ]; then
    cp $conf_copy_path/aria2.conf $conf_path/aria2.conf
fi

userid="$(id -u)" # 65534 - nobody, 0 - root
groupid="$(id -g)"

if [[ -n "$PUID" && -n "$PGID" ]]; then
    echo "Running as user $PUID:$PGID"
    userid=$PUID
    groupid=$PGID
fi

# chown -R "$userid":"$groupid" $conf_path
# chown -R "$userid":"$groupid" $data_path

# 启动 Caddy 服务
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 启动 Caddy 服务..."
caddy start --config /usr/local/caddy/Caddyfile --adapter=caddyfile
if [ $? -eq 0 ]; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Caddy 服务启动成功"
else
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Caddy 服务启动失败"
fi

# 检查是否存在预编译的 aria2-api 二进制文件
if [ -f "./aria2-api" ]; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 发现预编译的 aria2-api 二进制文件，启动 API 服务..."
    # 后台启动 API 服务
    ./aria2-api > /aria2/api.log 2>&1 &
    API_PID=$!
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] API 服务已启动，PID: $API_PID"

    # 等待一下确保服务启动
    sleep 2

    # 检查 API 服务是否正常运行
    if kill -0 $API_PID 2>/dev/null; then
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] API 服务运行正常，监听端口 8081"
    else
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] API 服务启动失败，请检查日志: /aria2/api.log"
    fi
else
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 未找到预编译的 aria2-api 二进制文件"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 请确保在 Docker 构建过程中编译了 Go 应用程序"
fi

# 启动 aria2c 服务（前台运行，保持容器运行）
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 启动 Aria2c 服务..."
aria2c "$@"
