name: Build Go Backend

on:
  push:
    branches: [ master, main ]
    paths:
      - '*.go'
      - 'go.mod'
      - 'go.sum'
      - '.github/workflows/build-go-backend.yml'
  pull_request:
    branches: [ master, main ]
    paths:
      - '*.go'
      - 'go.mod'
      - 'go.sum'
  workflow_dispatch:
  workflow_call:

jobs:
  build-go-backend:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.21'
          cache: true

      - name: Download dependencies
        run: go mod download

      - name: Verify dependencies
        run: go mod verify

      - name: Run tests
        run: go test -v ./...

      - name: Build Go backend
        run: |
          CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build -a -installsuffix cgo -ldflags="-w -s" -o aria2-api api.go db.go

      - name: Verify binary
        run: |
          file aria2-api
          ls -la aria2-api

      - name: Upload Go backend artifact
        uses: actions/upload-artifact@v4
        with:
          name: go-backend-${{ github.sha }}
          path: aria2-api
          retention-days: 30
          compression-level: 9

      - name: Upload Go backend artifact (latest)
        if: github.ref == 'refs/heads/master' || github.ref == 'refs/heads/main'
        uses: actions/upload-artifact@v4
        with:
          name: go-backend-latest
          path: aria2-api
          retention-days: 7
          compression-level: 9
