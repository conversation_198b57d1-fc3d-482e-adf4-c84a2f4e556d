name: Build Web Frontend

on:
  push:
    branches: [ master, main ]
    paths:
      - 'web/**'
      - '.github/workflows/build-web.yml'
  pull_request:
    branches: [ master, main ]
    paths:
      - 'web/**'
  workflow_dispatch:
  workflow_call:

jobs:
  build-web:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'web/package-lock.json'

      - name: Install dependencies
        working-directory: ./web
        run: npm ci

      - name: Build web application
        working-directory: ./web
        run: npm run build

      - name: Verify build output
        run: |
          echo "=== Web Build Output ==="
          ls -la dist/web/
          echo "=== Build size ==="
          du -sh dist/web/

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: web-build-${{ github.sha }}
          path: dist/web/
          retention-days: 30
          compression-level: 9

      - name: Upload build artifacts (latest)
        if: github.ref == 'refs/heads/master' || github.ref == 'refs/heads/main'
        uses: actions/upload-artifact@v4
        with:
          name: web-build-latest
          path: dist/web/
          retention-days: 7
          compression-level: 9