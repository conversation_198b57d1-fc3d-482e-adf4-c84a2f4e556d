name: Create and publish a Docker image

on:
  push:
    branches:
      - master
      - main
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # 构建 Go 后端
  build-go-backend:
    uses: ./.github/workflows/build-go-backend.yml

  # 构建 Web 前端
  build-web-frontend:
    uses: ./.github/workflows/build-web.yml

  # 构建并推送 Docker 镜像
  build-and-push-image:
    runs-on: ubuntu-latest
    needs: [build-go-backend, build-web-frontend]
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Download Go backend artifact
        uses: actions/download-artifact@v4
        with:
          name: go-backend-${{ github.sha }}
          path: .

      - name: Download Web frontend artifact
        uses: actions/download-artifact@v4
        with:
          name: web-build-${{ github.sha }}
          path: dist/web

      - name: Verify artifacts
        run: |
          echo "=== Go Backend Binary ==="
          ls -la aria2-api
          file aria2-api
          echo "=== Web Frontend Files ==="
          ls -la dist/web/
          echo "=== Total artifact sizes ==="
          du -sh aria2-api dist/web/

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          platforms: linux/amd64