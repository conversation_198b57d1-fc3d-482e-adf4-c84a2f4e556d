# IDE and Editor files
/.dev
/.idea
/*.iml
.vscode/
.history/

# Version control
/.git
/.gitignore

# Documentation and examples
/.res
/examples
/README.md
/CHANGELOG.md
/LICENSE
/.editorconfig
/.travis.yml
/build.sh
task.todo

# Go source files (we use pre-compiled binary)
*.go
go.mod
go.sum

# Web source files (we use pre-built dist)
web/src/
web/node_modules/
web/package.json
web/package-lock.json
web/tsconfig.json
web/vite.config.ts
web/tailwind.config.js
web/postcss.config.js
web/*.ts
web/*.vue

# Build artifacts that should come from GitHub Actions
# (Keep aria2-api and dist/web as they are provided by CI)

# Logs and temporary files
*.log
*.tmp
*.temp

# Database files
*.db
*.sqlite
*.sqlite3