#!/bin/bash
# Aria2 下载完成钩子脚本，通过 HTTP API 调用 /api/move-file 实现文件移动
# 适用于 Docker 环境

# ========== 配置区 ==========
API_HOST="${API_HOST:-127.0.0.1}"
API_PORT="${API_PORT:-8081}"
API_URL="http://${API_HOST}:${API_PORT}/api/move-file"
LOG_FILE="/aria2/api_move_hook.log"

# ========== 参数解析 ==========
GID="$1"
FILE_COUNT="$2"
FIRST_FILE_PATH="$3"
SOURCE_FOLDER="/aria2/ssd/"
TARGET_FOLDER="/aria2/hdd/"

# ========== 日志函数 ==========
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*" | tee -a "$LOG_FILE"
}

# ========== 参数校验 ==========
if [[ -z "$GID" || -z "$FILE_COUNT" || -z "$FIRST_FILE_PATH" ]]; then
    log "参数错误: GID=$GID, FILE_COUNT=$FILE_COUNT, FIRST_FILE_PATH=$FIRST_FILE_PATH"
    exit 1
fi

# ========== 构造JSON请求体 ==========
JSON_BODY=$(cat <<EOF
{
  "file_count_num": $FILE_COUNT,
  "first_file_path": "${FIRST_FILE_PATH}",
  "source_folder": "${SOURCE_FOLDER}",
  "target_folder": "${TARGET_FOLDER}"
}
EOF
)

log "调用API: $API_URL"
log "请求体: $JSON_BODY"

# ========== 调用API ==========
HTTP_RESP=$(curl -s -w "\n%{http_code}" -X POST "$API_URL" \
    -H "Content-Type: application/json" \
    -d "$JSON_BODY")

HTTP_BODY=$(echo "$HTTP_RESP" | head -n -1)
HTTP_CODE=$(echo "$HTTP_RESP" | tail -n1)

log "HTTP状态码: $HTTP_CODE"
log "响应内容: $HTTP_BODY"

if [[ "$HTTP_CODE" != "200" ]]; then
    log "API调用失败，状态码: $HTTP_CODE"
    exit 2
fi

STATUS=$(echo "$HTTP_BODY" | grep -o '"status"[ ]*:[ ]*"[^"]*"' | cut -d '"' -f4)
if [[ "$STATUS" != "success" ]]; then
    log "API返回失败状态: $STATUS"
    exit 3
fi

log "API调用成功，文件移动完成"
exit 0
