# 多阶段构建：第一阶段编译 Go 应用
FROM golang:1.21-alpine AS go-builder

# 安装构建依赖（SQLite 需要 CGO）
RUN apk add --no-cache gcc musl-dev sqlite-dev

WORKDIR /build

# 复制 Go 模块文件
COPY go.mod go.sum ./
RUN go mod download

# 复制 Go 源代码
COPY api.go db.go ./

# 编译 Go 应用
RUN CGO_ENABLED=1 GOOS=linux go build -a -installsuffix cgo -o aria2-api api.go db.go

# 第二阶段：运行时镜像
FROM alpine:latest

ENV ARIA2RPCPORT=8080

RUN apk update \
    && apk add --no-cache --update caddy aria2 su-exec curl

# AriaNG
WORKDIR /usr/local/www/ariang

RUN wget --no-check-certificate https://codeload.github.com/binux/yaaw/zip/refs/heads/master \
    -O ariang.zip \
    && unzip ariang.zip \
    && rm ariang.zip \
    && chmod -R 755 ./

WORKDIR /aria2

# 复制编译好的 Go 二进制文件
COPY --from=go-builder /build/aria2-api ./aria2-api
RUN chmod +x ./aria2-api

COPY aria2.conf ./conf-copy/aria2.conf
COPY start.sh ./
RUN chmod +x ./start.sh
COPY Caddyfile /usr/local/caddy/

VOLUME /aria2/data
VOLUME /aria2/conf

EXPOSE 8080 8081

ENTRYPOINT ["./start.sh"]
CMD ["--conf-path=/aria2/conf/aria2.conf"]
