FROM alpine:latest

ENV ARIA2RPCPORT=8080

RUN apk update \
    && apk add --no-cache --update caddy aria2 su-exec curl

# AriaNG
WORKDIR /usr/local/www/ariang

RUN wget --no-check-certificate https://codeload.github.com/binux/yaaw/zip/refs/heads/master \
    -O ariang.zip \
    && unzip ariang.zip \
    && rm ariang.zip \
    && chmod -R 755 ./

WORKDIR /aria2

# 复制预编译的 Go 二进制文件（由 GitHub Actions 构建）
COPY aria2-api ./aria2-api
RUN chmod +x ./aria2-api

# 复制 Web 前端构建产物（由 GitHub Actions 构建）
COPY dist/web /usr/local/www/web

COPY aria2.conf ./conf-copy/aria2.conf
COPY start.sh ./
RUN chmod +x ./start.sh
COPY Caddyfile /usr/local/caddy/

VOLUME /aria2/data
VOLUME /aria2/conf

EXPOSE 8080 8081

ENTRYPOINT ["./start.sh"]
CMD ["--conf-path=/aria2/conf/aria2.conf"]
