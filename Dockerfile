FROM alpine:latest

ENV ARIA2RPCPORT=8080

# 安装必要的包、下载 YAAW 并清理缓存（合并所有操作减少镜像层数）
RUN apk add --no-cache --update \
    caddy \
    aria2 \
    su-exec \
    curl \
    unzip \
    && mkdir -p /usr/local/www/ariang \
    && cd /usr/local/www/ariang \
    && wget --no-check-certificate \
        https://codeload.github.com/binux/yaaw/zip/refs/heads/master \
        -O yaaw.zip \
    && unzip yaaw.zip \
    && rm yaaw.zip \
    && chmod -R 755 yaaw-master \
    && apk del unzip \
    && rm -rf /var/cache/apk/* /tmp/* /var/tmp/* /usr/share/man/* /usr/share/doc/*

# 设置工作目录并复制所有文件（合并操作）
WORKDIR /aria2

# 复制所有必要文件并设置权限（合并操作减少层数）
COPY aria2-api ./aria2-api
COPY dist/web /app/web
COPY aria2.conf ./conf-copy/aria2.conf
COPY start.sh ./
COPY Caddyfile /usr/local/caddy/

# 设置执行权限并创建必要目录
RUN chmod +x ./aria2-api ./start.sh \
    && mkdir -p /aria2/data /aria2/conf /app \
    && rm -rf /tmp/* /var/tmp/*

# 配置卷和端口（API 服务仅内部访问，通过 Caddy 代理）
VOLUME ["/aria2/data", "/aria2/conf"]
EXPOSE 8080 8090

ENTRYPOINT ["./start.sh"]
CMD ["--conf-path=/aria2/conf/aria2.conf"]
